// TESLA ÖDEME SCRİPTİ - https://static-assets-pay.tesla.com/v5/
const userInfo = {
    email: "<EMAIL>",
    phoneNumber: "5325661533",
    firstName: "SIDKI",
    lastName: "HAYZARAN",
    tckn: "23815915734",
    street: "2085 sok no 5",
    city: "ankara",
    stateProvince: "ankara",
    zip: "06650",
    cardNumber: "****************", // Test kartı
    expiryMonth: "12",
    expiryYear: "2025",
    cvv: "123",
    cardHolderName: "SIDKI HAYZARAN"
};

class TeslaPayment {
    constructor(userInfo) {
        this.userInfo = userInfo;
        this.authToken = null;
        this.ssoToken = null;
        this.extractTokensFromCookies();
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) return element;
            await this.wait(100);
        }
        throw new Error(`Element bulunamadı: ${selector}`);
    }

    async fillInput(selector, value) {
        const input = await this.waitForElement(selector);
        input.focus();
        input.value = '';
        input.value = value;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        await this.wait(500);
    }

    async clickButton(selector) {
        const button = await this.waitForElement(selector);
        button.scrollIntoView();
        await this.wait(500);
        button.click();
        await this.wait(1000);
    }

    extractTokensFromCookies() {
        console.log('🔑 Cookielerden tokenlar aliniyor...');

        try {
            const cookies = document.cookie.split(';');

            for (const cookie of cookies) {
                const [name, value] = cookie.trim().split('=');

                if (name === 'authTeslaWebToken') {
                    this.authToken = value;
                    console.log('✅ Auth token alındı');
                }

                if (name === 'teslaSSOIdToken') {
                    this.ssoToken = value;
                    console.log('✅ SSO token alındı');
                }
            }

        } catch (error) {
            console.log('⚠️ Token alma hatası:', error);
        }
    }

    setupApiHeaders() {
        console.log('🔧 API headerlari ayarlaniyor...');

        const originalFetch = window.fetch;

        window.fetch = async (url, options = {}) => {
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': navigator.userAgent,
                ...options.headers
            };

            if (this.authToken) {
                headers['Authorization'] = `Bearer ${this.authToken}`;
            }

            if (this.ssoToken) {
                headers['X-Tesla-SSO-Token'] = this.ssoToken;
            }

            if (url.includes('tesla.com') || url.includes('static-assets-pay.tesla.com')) {
                headers['X-Requested-With'] = 'XMLHttpRequest';
                headers['Referer'] = window.location.href;
                headers['Origin'] = window.location.origin;
            }

            return originalFetch(url, {
                ...options,
                headers
            });
        };

        console.log('✅ API headerlari ayarlandi');
    }

    async fillPaymentInfo() {
        console.log('💳 Kredi kartı bilgileri dolduruluyor...');
        
        if (!window.location.href.includes('static-assets-pay.tesla.com')) {
            console.log('❌ Bu script sadece Tesla ödeme sayfasında çalışır');
            return false;
        }
        
        await this.wait(3000);
        
        const paymentFields = [
            { selector: 'input[name="cardNumber"], #cardNumber, [data-testid="card-number"], .card-number-input', value: this.userInfo.cardNumber },
            { selector: 'input[name="expiryMonth"], #expiryMonth, [data-testid="expiry-month"], .expiry-month-input', value: this.userInfo.expiryMonth },
            { selector: 'input[name="expiryYear"], #expiryYear, [data-testid="expiry-year"], .expiry-year-input', value: this.userInfo.expiryYear },
            { selector: 'input[name="cvv"], #cvv, [data-testid="cvv"], .cvv-input', value: this.userInfo.cvv },
            { selector: 'input[name="cardHolderName"], #cardHolderName, [data-testid="cardholder-name"], .cardholder-name-input', value: this.userInfo.cardHolderName }
        ];

        for (const field of paymentFields) {
            try {
                await this.fillInput(field.selector, field.value);
                console.log(`✅ ${field.selector.split(',')[0]} alanı dolduruldu`);
            } catch (error) {
                console.log(`⚠️ Ödeme alanı doldurma hatası: ${field.selector.split(',')[0]}`);
            }
        }
        
        await this.fillBillingAddress();
        return true;
    }

    async fillBillingAddress() {
        console.log('🏠 Fatura adresi bilgileri dolduruluyor...');
        
        const billingFields = [
            { selector: 'input[name="firstName"], #firstName, [data-testid="billing-first-name"]', value: this.userInfo.firstName },
            { selector: 'input[name="lastName"], #lastName, [data-testid="billing-last-name"]', value: this.userInfo.lastName },
            { selector: 'input[name="address"], #address, [data-testid="billing-address"]', value: this.userInfo.street },
            { selector: 'input[name="city"], #city, [data-testid="billing-city"]', value: this.userInfo.city },
            { selector: 'input[name="postalCode"], #postalCode, [data-testid="billing-postal-code"]', value: this.userInfo.zip },
            { selector: 'input[name="phone"], #phone, [data-testid="billing-phone"]', value: this.userInfo.phoneNumber }
        ];

        for (const field of billingFields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Fatura adresi alanı doldurma hatası: ${field.selector.split(',')[0]}`);
            }
        }
    }

    async acceptTerms() {
        console.log('📋 Sözleşmeler kabul ediliyor...');
        
        try {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            for (const checkbox of checkboxes) {
                if (!checkbox.checked) {
                    checkbox.click();
                    await this.wait(500);
                    console.log('✅ Sözleşme kabul edildi');
                }
            }
        } catch (error) {
            console.log('⚠️ Sözleşme kabul hatası:', error);
        }
    }

    async completePayment() {
        console.log('💰 Tesla odeme sayfasinda odeme tamamlaniyor...');

        try {
            // API header'larını ayarla
            this.setupApiHeaders();

            // Token kontrolü
            if (!this.authToken || !this.ssoToken) {
                console.log('⚠️ Tokenlar eksik, tekrar aliniyor...');
                this.extractTokensFromCookies();
            }

            const paymentFilled = await this.fillPaymentInfo();
            if (!paymentFilled) {
                throw new Error('Odeme bilgileri doldurulama');
            }

            await this.acceptTerms();

            const paymentButtons = [
                '[data-testid="complete-payment"]',
                '.complete-payment-btn',
                'button:contains("Odemeyi Tamamla")',
                'button:contains("Complete Payment")',
                '.pay-now-btn'
            ];

            for (const selector of paymentButtons) {
                try {
                    await this.clickButton(selector);
                    console.log('✅ Odeme butonu tiklandi');
                    break;
                } catch (e) {
                    continue;
                }
            }

            await this.waitForElement('[data-testid="payment-success"], .payment-success, .order-confirmation, .success-message', 30000);

            console.log('🎉 Tesla odeme basariyla tamamlandi!');
            return true;

        } catch (error) {
            console.error('❌ Tesla odeme hatasi:', error);
            return false;
        }
    }

    // Manuel token ayarlama
    setTokens(authToken, ssoToken) {
        this.authToken = authToken;
        this.ssoToken = ssoToken;
        console.log('✅ Tokenlar manuel olarak ayarlandi');
    }

    // Token durumunu kontrol et
    checkTokens() {
        console.log('🔍 Token durumu:');
        console.log(`Auth Token: ${this.authToken ? 'Mevcut ✅' : 'Eksik ❌'}`);
        console.log(`SSO Token: ${this.ssoToken ? 'Mevcut ✅' : 'Eksik ❌'}`);
    }
}

const teslaPayment = new TeslaPayment(userInfo);

console.log(`
💳 TESLA ÖDEME SCRİPTİ

YAPIŞTIRILACAK KOD:
teslaPayment.completePayment();

Bu script sadece Tesla ödeme sayfasında (https://static-assets-pay.tesla.com/v5/) çalışır.
`);
