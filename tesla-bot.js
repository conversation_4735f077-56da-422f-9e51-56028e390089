// TESLA MODEL Y JUNIPER REZERVASYON BOT
const userInfo = {
    email: "<EMAIL>",
    password: "?r8YN3t-7h^G$v/",
    phoneNumber: "5325661533",
    firstName: "SIDKI",
    lastName: "HAYZARAN",
    tckn: "23815915734",
    street: "2085 sok no 5",
    city: "ankara",
    stateProvince: "ankara",
    zip: "06650"
};

const modelYConfig = {
    model: "Model Y Arkadan Çekiş",
    exteriorColor: "Quicksilver",
    interiorColor: "All Black Premium İç Mekan",
    wheels: "19\" Crossflow Jantlar",
    expectedPrice: "1.913.987",
    country: "TR"
};

class TeslaModelYReservation {
    constructor(userInfo, modelYConfig) {
        this.userInfo = userInfo;
        this.modelYConfig = modelYConfig;
        this.reservationId = null;
        this.vinNumber = null;
        this.finalPrice = null;
        this.baseUrl = 'https://www.tesla.com/tr_tr';
        this.authToken = null;
        this.ssoToken = null;
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) return element;
            await this.wait(100);
        }
        throw new Error(`Element bulunamadı: ${selector}`);
    }

    async fillInput(selector, value) {
        const input = await this.waitForElement(selector);
        input.focus();
        input.value = '';
        input.value = value;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        await this.wait(500);
    }

    async clickButton(selector) {
        const button = await this.waitForElement(selector);
        button.scrollIntoView();
        await this.wait(500);
        button.click();
        await this.wait(1000);
    }

    async extractTokens() {
        console.log('🔑 JWT tokenları alınıyor...');

        try {
            const cookies = document.cookie.split(';');

            for (const cookie of cookies) {
                const [name, value] = cookie.trim().split('=');

                if (name === 'authTeslaWebToken') {
                    this.authToken = value;
                    console.log('✅ Auth token alındı');
                }

                if (name === 'teslaSSOIdToken') {
                    this.ssoToken = value;
                    console.log('✅ SSO token alındı');
                }
            }

            try {
                const localAuthToken = localStorage.getItem('authTeslaWebToken');
                const localSSOToken = localStorage.getItem('teslaSSOIdToken');

                if (localAuthToken && !this.authToken) {
                    this.authToken = localAuthToken;
                    console.log('✅ Auth token (localStorage) alındı');
                }

                if (localSSOToken && !this.ssoToken) {
                    this.ssoToken = localSSOToken;
                    console.log('✅ SSO token (localStorage) alındı');
                }
            } catch (e) {
                console.log('⚠️ localStorage erişim hatası');
            }

        } catch (error) {
            console.log('⚠️ Token alma hatası:', error);
        }
    }

    async login() {
        console.log('🔐 Giriş yapılıyor...');

        try {
            // Zaten giriş yapılmış mı kontrol et
            if (document.querySelector('[data-testid="account-menu"], .user-menu, .profile-menu')) {
                console.log('✅ Zaten giriş yapılmış!');
                await this.extractTokens();
                return true;
            }

            if (!window.location.href.includes('tesla.com/tr_tr')) {
                window.location.href = this.baseUrl;
                await this.wait(3000);
            }

            // Giriş butonunu bul
            const loginSelectors = [
                'a[href*="account"]',
                '.account-link',
                '[data-testid="account-button"]',
                'a:contains("Hesap")',
                'a:contains("Account")',
                '.header-account'
            ];

            let loginClicked = false;
            for (const selector of loginSelectors) {
                try {
                    await this.clickButton(selector);
                    loginClicked = true;
                    break;
                } catch (e) {
                    continue;
                }
            }

            if (!loginClicked) {
                console.log('⚠️ Giriş butonu bulunamadı, URL ile deneniyor...');
                window.location.href = 'https://auth.tesla.com/oauth2/v1/authorize?redirect_uri=https%3A%2F%2Fwww.tesla.com%2Fteslaaccount%2Fowner-xp&response_type=code&client_id=ownership&scope=offline_access%20openid%20ou_code%20email%20phone&audience=https%3A%2F%2Fownership.tesla.com%2F&locale=tr-TR';
                await this.wait(3000);
            }

            // Email girişi
            await this.fillInput('input[type="email"], input[name="email"], #email', this.userInfo.email);
            await this.clickButton('button[type="submit"], .btn-primary, [data-testid="continue-button"], button:contains("Devam")');

            await this.wait(2000);

            // Şifre girişi
            await this.fillInput('input[type="password"], input[name="password"], #password', this.userInfo.password);
            await this.clickButton('button[type="submit"], .btn-primary, [data-testid="sign-in-button"], button:contains("Giriş")');

            // Giriş başarılı mı kontrol et
            await this.waitForElement('[data-testid="account-menu"], .user-menu, .profile-menu', 30000);

            await this.extractTokens();

            console.log('✅ Giriş başarılı!');
            return true;
        } catch (error) {
            console.error('❌ Giriş hatası:', error);
            console.log('🔄 Manuel giriş yapmanız gerekebilir...');
            return false;
        }
    }

    async selectModelYJuniper() {
        console.log('🚗 Model Y Juniper seçiliyor...');

        try {
            window.location.href = `${this.baseUrl}/modely/design`;
            await this.wait(3000);

            await this.selectRearWheelDrive();
            await this.configureModelY();
            await this.checkPriceAndVIN();

            console.log('✅ Model Y seçimi tamamlandı!');
            console.log(`🏷️ VIN: ${this.vinNumber}`);
            console.log(`💰 Fiyat: ${this.finalPrice} TL`);

            return true;
        } catch (error) {
            console.error('❌ Model Y seçim hatası:', error);
            return false;
        }
    }

    async selectRearWheelDrive() {
        const rearWheelOptions = [
            '[data-testid="rwd-option"]',
            '.rwd-option',
            'button:contains("Arkadan Çekiş")',
            '[data-variant="rwd"]'
        ];

        for (const selector of rearWheelOptions) {
            try {
                await this.clickButton(selector);
                break;
            } catch (e) {
                continue;
            }
        }
        await this.wait(2000);
    }

    async configureModelY() {
        await this.selectExteriorColor();
        await this.selectInteriorColor();
        await this.selectWheels();
        await this.wait(2000);
    }

    async selectExteriorColor() {
        const colorSelectors = [
            '[data-testid="quicksilver-color"]',
            '.color-quicksilver',
            'button[aria-label*="Quicksilver"]',
            '.paint-option:contains("Quicksilver")'
        ];

        for (const selector of colorSelectors) {
            try {
                await this.clickButton(selector);
                console.log('✅ Quicksilver renk seçildi');
                break;
            } catch (e) {
                continue;
            }
        }
        await this.wait(1500);
    }

    async selectInteriorColor() {
        const interiorSelectors = [
            '[data-testid="all-black-premium"]',
            '.interior-all-black-premium',
            'button[aria-label*="All Black Premium"]',
            '.interior-option:contains("All Black Premium")'
        ];

        for (const selector of interiorSelectors) {
            try {
                await this.clickButton(selector);
                console.log('✅ All Black Premium iç mekan seçildi');
                break;
            } catch (e) {
                continue;
            }
        }
        await this.wait(1500);
    }

    async selectWheels() {
        const wheelSelectors = [
            '[data-testid="19-crossflow-wheels"]',
            '.wheels-19-crossflow',
            'button[aria-label*="19\\" Crossflow"]',
            '.wheel-option:contains("19\\" Crossflow")'
        ];

        for (const selector of wheelSelectors) {
            try {
                await this.clickButton(selector);
                console.log('✅ 19" Crossflow jantlar seçildi');
                break;
            } catch (e) {
                continue;
            }
        }
        await this.wait(1500);
    }

    async checkPriceAndVIN() {
        const priceSelectors = [
            '[data-testid="total-price"]',
            '.total-price',
            '.price-display',
            '.final-price'
        ];

        for (const selector of priceSelectors) {
            try {
                const priceElement = await this.waitForElement(selector, 3000);
                this.finalPrice = priceElement.textContent.trim();
                console.log(`💰 Fiyat: ${this.finalPrice}`);
                break;
            } catch (e) {
                continue;
            }
        }

        const vinSelectors = [
            '[data-testid="vin-number"]',
            '.vin-display',
            '.vehicle-vin',
            'span:contains("VIN")'
        ];

        for (const selector of vinSelectors) {
            try {
                const vinElement = await this.waitForElement(selector, 3000);
                this.vinNumber = vinElement.textContent.trim();
                console.log(`🏷️ VIN: ${this.vinNumber}`);
                break;
            } catch (e) {
                continue;
            }
        }
    }

    async fillPersonalInfo() {
        const fields = [
            { selector: 'input[name="firstName"], #firstName, [data-testid="first-name"]', value: this.userInfo.firstName },
            { selector: 'input[name="lastName"], #lastName, [data-testid="last-name"]', value: this.userInfo.lastName },
            { selector: 'input[name="phone"], #phone, [data-testid="phone"]', value: this.userInfo.phoneNumber },
            { selector: 'input[name="email"], #email, [data-testid="email"]', value: this.userInfo.email }
        ];

        for (const field of fields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Alan doldurma hatası: ${field.selector}`);
            }
        }
    }

    async fillDeliveryInfo() {
        const addressFields = [
            { selector: 'input[name="street"], #street, [data-testid="street"]', value: this.userInfo.street },
            { selector: 'input[name="city"], #city, [data-testid="city"]', value: this.userInfo.city },
            { selector: 'input[name="state"], #state, [data-testid="state"]', value: this.userInfo.stateProvince },
            { selector: 'input[name="zip"], #zip, [data-testid="zip"]', value: this.userInfo.zip }
        ];

        for (const field of addressFields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Adres alanı doldurma hatası: ${field.selector}`);
            }
        }
    }
    async makeReservation() {
        console.log('📝 Rezervasyon yapılıyor...');

        try {
            await this.fillPersonalInfo();
            await this.fillDeliveryInfo();
            await this.payReservationFee();

            console.log('✅ Rezervasyon tamamlandı!');
            return true;
        } catch (error) {
            console.error('❌ Rezervasyon hatası:', error);
            return false;
        }
    }

    async payReservationFee() {
        try {
            await this.prepareApiHeaders();

            await this.clickButton('[data-testid="order-button"], .order-btn, button:contains("Sipariş Ver"), button:contains("Order Now")');
            await this.wait(3000);
            await this.displayReservationInfo();
            console.log('🔄 Tesla ödeme sayfasına yönlendiriliyor...');
            await this.waitForElement('[data-testid="payment-success"], .payment-success, .success-message, .reservation-confirmed', 15000);
        } catch (error) {
            console.error('❌ Rezervasyon ödeme hatası:', error);
            throw error;
        }
    }

    async prepareApiHeaders() {
        console.log('🔧 API headerları hazırlanıyor...');

        try {
            const originalFetch = window.fetch;

            window.fetch = async (url, options = {}) => {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'User-Agent': navigator.userAgent,
                    ...options.headers
                };

                if (this.authToken) {
                    headers['Authorization'] = `Bearer ${this.authToken}`;
                }

                if (this.ssoToken) {
                    headers['X-Tesla-SSO-Token'] = this.ssoToken;
                }

                if (url.includes('tesla.com') || url.includes('static-assets-pay.tesla.com')) {
                    headers['X-Requested-With'] = 'XMLHttpRequest';
                    headers['Referer'] = window.location.href;
                    headers['Origin'] = window.location.origin;
                }

                return originalFetch(url, {
                    ...options,
                    headers
                });
            };

            console.log('✅ API headerları hazırlandı');

        } catch (error) {
            console.log('⚠️ API header hazırlama hatası:', error);
        }
    }

    async displayReservationInfo() {
        const reservationInfo = {
            model: this.modelYConfig.model,
            exteriorColor: this.modelYConfig.exteriorColor,
            interiorColor: this.modelYConfig.interiorColor,
            wheels: this.modelYConfig.wheels,
            vin: this.vinNumber,
            price: this.finalPrice,
            customerName: `${this.userInfo.firstName} ${this.userInfo.lastName}`,
            email: this.userInfo.email
        };

        console.log('🚗 REZERVASYON BİLGİLERİ:');
        console.log('═══════════════════════════════');
        console.log(`📱 Model: ${reservationInfo.model}`);
        console.log(`🎨 Dış Renk: ${reservationInfo.exteriorColor}`);
        console.log(`🪑 İç Renk: ${reservationInfo.interiorColor}`);
        console.log(`🛞 Jantlar: ${reservationInfo.wheels}`);
        console.log(`🏷️ VIN: ${reservationInfo.vin || 'Henüz atanmadı'}`);
        console.log(`💰 Fiyat: ${reservationInfo.price || this.modelYConfig.expectedPrice + ' TL'}`);
        console.log(`👤 Müşteri: ${reservationInfo.customerName}`);
        console.log(`📧 Email: ${reservationInfo.email}`);
        console.log('═══════════════════════════════');

        try {
            const reservationElement = await this.waitForElement('[data-testid="reservation-id"], .reservation-number', 5000);
            this.reservationId = reservationElement.textContent.trim();
            console.log(`🎫 Rezervasyon ID: ${this.reservationId}`);
        } catch (e) {
            console.log('⚠️ Rezervasyon ID henüz oluşturulmadı');
        }
    }

    async checkVehicleAvailability() {
        console.log('🔍 Araç müsaitlik kontrolü yapılıyor...');

        try {
            // Model Y sayfasına git
            if (!window.location.href.includes('/modely/design')) {
                window.location.href = `${this.baseUrl}/modely/design`;
                await this.wait(3000);
            }

            // Araç müsait mi kontrol et
            const availabilitySelectors = [
                '.inventory-available',
                '[data-testid="vehicle-available"]',
                '.order-now-available',
                'button:contains("Order Now")',
                'button:contains("Sipariş Ver")'
            ];

            for (const selector of availabilitySelectors) {
                try {
                    const element = document.querySelector(selector);
                    if (element && !element.disabled) {
                        console.log('✅ ARAÇ MÜSAİT! Rezervasyon başlatılıyor...');
                        return true;
                    }
                } catch (e) {
                    continue;
                }
            }

            // Stok durumu mesajlarını kontrol et
            const stockMessages = document.querySelectorAll('*');
            for (const element of stockMessages) {
                const text = element.textContent?.toLowerCase() || '';
                if (text.includes('stokta yok') || text.includes('müsait değil') || text.includes('out of stock')) {
                    console.log('❌ Araç stokta yok');
                    return false;
                }
            }

            console.log('⚠️ Araç durumu belirsiz, tekrar kontrol edilecek...');
            return false;

        } catch (error) {
            console.log('⚠️ Müsaitlik kontrol hatası:', error);
            return false;
        }
    }

    async startVehicleMonitoring() {
        console.log('🔄 Araç izleme başlatılıyor...');
        console.log('⏰ Her 2 saniyede bir kontrol edilecek...');

        // Önce giriş yap
        const loginSuccess = await this.login();
        if (!loginSuccess) {
            console.error('❌ Giriş yapılamadı, izleme durduruluyor');
            return;
        }

        // Sürekli kontrol döngüsü
        const checkInterval = setInterval(async () => {
            try {
                const isAvailable = await this.checkVehicleAvailability();

                if (isAvailable) {
                    console.log('🎉 ARAÇ BULUNDU! Rezervasyon başlatılıyor...');
                    clearInterval(checkInterval);

                    // Rezervasyon sürecini başlat
                    await this.startReservation();
                } else {
                    const now = new Date().toLocaleTimeString();
                    console.log(`⏰ ${now} - Araç henüz müsait değil, bekleniyor...`);
                }

            } catch (error) {
                console.error('❌ Kontrol hatası:', error);
            }
        }, 2000); // 2 saniyede bir kontrol

        // 30 dakika sonra otomatik durdur
        setTimeout(() => {
            clearInterval(checkInterval);
            console.log('⏰ 30 dakika doldu, izleme durduruluyor');
        }, 30 * 60 * 1000);
    }

    async startReservation() {
        console.log('🚀 Tesla Model Y Juniper rezervasyon başlatılıyor...');

        try {
            await this.extractTokens();

            if (!this.authToken || !this.ssoToken) {
                console.log('⚠️ Tokenlar eksik, tekrar deneniyor...');
                await this.wait(2000);
                await this.extractTokens();
            }

            await this.wait(2000);

            const vehicleSuccess = await this.selectModelYJuniper();
            if (!vehicleSuccess) throw new Error('Model Y seçilemedi');

            await this.wait(2000);

            const reservationSuccess = await this.makeReservation();
            if (!reservationSuccess) throw new Error('Rezervasyon yapılamadı');

            console.log('🎉 Model Y Juniper rezervasyon tamamlandı!');
            console.log('💳 Ödeme için ayrı scripti kullanın...');
            console.log(`🔑 Auth Token: ${this.authToken ? 'Mevcut' : 'Eksik'}`);
            console.log(`🔑 SSO Token: ${this.ssoToken ? 'Mevcut' : 'Eksik'}`);

            return true;

        } catch (error) {
            console.error('❌ Rezervasyon hatası:', error);
            return false;
        }
    }

    setTokens(authToken, ssoToken) {
        this.authToken = authToken;
        this.ssoToken = ssoToken;
        console.log('✅ Tokenlar manuel olarak ayarlandı');
    }

    checkTokens() {
        console.log('🔍 Token durumu:');
        console.log(`Auth Token: ${this.authToken ? 'Mevcut ✅' : 'Eksik ❌'}`);
        console.log(`SSO Token: ${this.ssoToken ? 'Mevcut ✅' : 'Eksik ❌'}`);

        if (this.authToken) {
            try {
                const payload = JSON.parse(atob(this.authToken.split('.')[1]));
                console.log(`Token süresi: ${new Date(payload.exp * 1000).toLocaleString()}`);
            } catch (e) {
                console.log('Token decode hatası');
            }
        }
    }
}

// Bot'u başlat
const teslaReservation = new TeslaModelYReservation(userInfo, modelYConfig);

// Bot aktif durumu göster
console.log(`
🤖 TESLA BOT AKTİF!
═══════════════════════════════════════════════════════════
🚗 Model Y Juniper Rezervasyon Botu Hazır
🔑 Tokenlar otomatik alınacak
⚡ Sistem aktif ve çalışmaya hazır
═══════════════════════════════════════════════════════════

📋 KULLANIM:
startTeslaBot();
`);

// Tokenları hemen al
teslaReservation.extractTokens();

// Global fonksiyonlar tanımla
window.startTeslaBot = function() {
    console.log('🚀 TESLA BOT BAŞLATILIYOR...');
    console.log('🔄 Araç izleme modu aktif!');
    teslaReservation.extractTokens();
    teslaReservation.startVehicleMonitoring();
};

window.startDirectReservation = function() {
    console.log('🚀 DİREKT REZERVASYON BAŞLATILIYOR...');
    teslaReservation.extractTokens();
    teslaReservation.startReservation();
};

window.refreshTokens = function() {
    console.log('🔄 TOKENLAR YENİLENİYOR...');
    teslaReservation.extractTokens();
    teslaReservation.checkTokens();
};

window.checkBotStatus = function() {
    console.log('🔍 BOT DURUMU:');
    teslaReservation.checkTokens();
    console.log(`📍 Mevcut URL: ${window.location.href}`);
    console.log(`🌐 Tesla sitesinde: ${window.location.href.includes('tesla.com') ? 'Evet ✅' : 'Hayır ❌'}`);
};

window.checkVehicleNow = function() {
    console.log('🔍 ARAÇ KONTROL EDİLİYOR...');
    teslaReservation.checkVehicleAvailability();
};

console.log(`
🎯 YAPIŞTIRILACAK KODLAR:

1️⃣ ARAÇ İZLEME MODU (Önerilen):
startTeslaBot();

2️⃣ DİREKT REZERVASYON:
startDirectReservation();

🔧 DİĞER KOMUTLAR:
refreshTokens();        // Tokenları yenile
checkBotStatus();       // Bot durumunu kontrol et
checkVehicleNow();      // Şu an araç kontrol et

⚠️ NOT: startTeslaBot() her 2 saniyede araç kontrol eder!
`);
