// TESLA ARAÇ İZLEME BOT - BASİT VERSİYON
console.log(`
🤖 TESLA ARAÇ İZLEME BOT AKTİF!
═══════════════════════════════════════════════════════════
🚗 Model Y Juniper İzleme Botu
⚡ Manuel giriş sonrası çalışır
🔍 Her 2 saniyede araç kontrol eder
═══════════════════════════════════════════════════════════
`);

class SimpleTeslaBot {
    constructor() {
        this.isRunning = false;
        this.checkCount = 0;
        this.interval = null;
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    checkVehicleAvailability() {
        this.checkCount++;
        const now = new Date().toLocaleTimeString();
        
        console.log(`🔍 KONTROL #${this.checkCount} - ${now}`);
        console.log(`📍 Mevcut URL: ${window.location.href}`);
        
        // Model Y sayfasında mı kontrol et
        if (!window.location.href.includes('/modely')) {
            console.log('⚠️ Model Y sayfasına gidin: https://www.tesla.com/tr_tr/modely/design');
            return false;
        }
        
        // Sipariş butonlarını ara
        const buttons = document.querySelectorAll('button, a, [role="button"]');
        let foundOrderButton = false;
        
        for (const button of buttons) {
            const text = button.textContent?.trim().toLowerCase() || '';
            const isVisible = button.offsetParent !== null;
            const isEnabled = !button.disabled;
            
            if (isVisible && isEnabled && 
                (text.includes('order') || 
                 text.includes('sipariş') || 
                 text.includes('şimdi sipariş'))) {
                console.log('✅ SİPARİŞ BUTONU BULUNDU!');
                console.log(`📝 Buton: "${button.textContent.trim()}"`);
                foundOrderButton = true;
                
                // Butonu vurgula
                button.style.border = '3px solid red';
                button.style.backgroundColor = 'yellow';
                
                return true;
            }
        }
        
        // Stok durumu kontrol et
        const pageText = document.body.textContent.toLowerCase();
        if (pageText.includes('stokta yok') || 
            pageText.includes('müsait değil') || 
            pageText.includes('out of stock')) {
            console.log('❌ Stokta yok');
        } else if (!foundOrderButton) {
            console.log('❌ Sipariş butonu bulunamadı');
        }
        
        // Sayfa elementlerini listele (debug için)
        if (this.checkCount % 10 === 0) {
            console.log('🔍 Sayfadaki butonlar:');
            buttons.forEach((btn, index) => {
                if (index < 5) { // İlk 5 butonu göster
                    console.log(`  ${index + 1}. "${btn.textContent?.trim()}" - Görünür: ${btn.offsetParent !== null}`);
                }
            });
        }
        
        return false;
    }

    startMonitoring() {
        if (this.isRunning) {
            console.log('⚠️ Bot zaten çalışıyor!');
            return;
        }
        
        console.log('🚀 ARAÇ İZLEME BAŞLATILIYOR...');
        console.log('⏰ Her 2 saniyede kontrol edilecek');
        console.log('📍 Model Y sayfasında olduğunuzdan emin olun!');
        
        this.isRunning = true;
        
        this.interval = setInterval(() => {
            try {
                const found = this.checkVehicleAvailability();
                
                if (found) {
                    console.log('🎉 ARAÇ BULUNDU! İzleme durduruluyor...');
                    console.log('🔔 HEMEN SİPARİŞ VERİN!');
                    this.stopMonitoring();
                    
                    // Ses çıkar (mümkünse)
                    try {
                        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                        audio.play();
                    } catch (e) {
                        console.log('🔔 SES ÇALINAMADI');
                    }
                }
                
            } catch (error) {
                console.error('❌ Kontrol hatası:', error);
            }
        }, 2000);
        
        // 30 dakika sonra durdur
        setTimeout(() => {
            if (this.isRunning) {
                console.log('⏰ 30 dakika doldu, izleme durduruluyor');
                this.stopMonitoring();
            }
        }, 30 * 60 * 1000);
    }

    stopMonitoring() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        this.isRunning = false;
        console.log('⏹️ İzleme durduruldu');
    }

    getStatus() {
        console.log('🔍 BOT DURUMU:');
        console.log(`▶️ Çalışıyor: ${this.isRunning ? 'Evet ✅' : 'Hayır ❌'}`);
        console.log(`📊 Kontrol sayısı: ${this.checkCount}`);
        console.log(`📍 Mevcut URL: ${window.location.href}`);
        console.log(`🌐 Model Y sayfasında: ${window.location.href.includes('/modely') ? 'Evet ✅' : 'Hayır ❌'}`);
    }

    testNow() {
        console.log('🧪 TEK KONTROL YAPILIYOR...');
        const result = this.checkVehicleAvailability();
        console.log(`📊 Sonuç: ${result ? 'ARAÇ MÜSAİT ✅' : 'ARAÇ MÜSAİT DEĞİL ❌'}`);
        return result;
    }
}

// Bot'u oluştur
const teslaBot = new SimpleTeslaBot();

// Global fonksiyonlar
window.startBot = function() {
    teslaBot.startMonitoring();
};

window.stopBot = function() {
    teslaBot.stopMonitoring();
};

window.botStatus = function() {
    teslaBot.getStatus();
};

window.testBot = function() {
    teslaBot.testNow();
};

console.log(`
🎯 KULLANIM KOMUTLARI:

startBot();     // İzlemeyi başlat
stopBot();      // İzlemeyi durdur  
botStatus();    // Bot durumunu kontrol et
testBot();      // Tek kontrol yap

📋 ADIMLAR:
1. Tesla.com/tr_tr/modely/design sayfasına gidin
2. Manuel giriş yapın
3. startBot(); komutunu çalıştırın
4. Console'u açık tutun

⚠️ ÖNEMLİ: Model Y sayfasında olduğunuzdan emin olun!
`);
