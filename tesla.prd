// Tesla Otomatik Satış Scripti - Kullanıcı Bilgileri
const userInfo = {
    email: "<EMAIL>",
    phoneNumber: "5325661533",
    firstName: "SIDKI",
    lastName: "HAYZARAN",
    tckn: "23815915734",
    street: "2085 sok no 5",
    city: "ankara",
    stateProvince: "ankara",
    zip: "06650",
    // Kredi kartı bilgileri (güvenlik için şifrelenmiş olarak saklanmalı)
    cardNumber: "****************", // Test kartı
    expiryMonth: "12",
    expiryYear: "2025",
    cvv: "123",
    cardHolderName: "SIDKI HAYZARAN"
};

// Tesla Otomatik Satış Süreci
class TeslaAutoBuyer {
    constructor(userInfo) {
        this.userInfo = userInfo;
        this.currentStep = 'login';
        this.selectedVehicle = null;
        this.reservationId = null;
    }

    // Bekleme fonksiyonu
    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Element bulma ve bekleme
    async waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) return element;
            await this.wait(100);
        }
        throw new Error(`Element bulunamadı: ${selector}`);
    }

    // Form doldurma
    async fillInput(selector, value) {
        const input = await this.waitForElement(selector);
        input.focus();
        input.value = '';
        input.value = value;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        await this.wait(500);
    }

    // Butona tıklama
    async clickButton(selector) {
        const button = await this.waitForElement(selector);
        button.scrollIntoView();
        await this.wait(500);
        button.click();
        await this.wait(1000);
    }

    // 1. Adım: Giriş yapma
    async login() {
        console.log('🔐 Giriş yapılıyor...');

        try {
            // Email girişi
            await this.fillInput('input[type="email"], input[name="email"], #email', this.userInfo.email);

            // Devam butonu
            await this.clickButton('button[type="submit"], .btn-primary, [data-testid="continue-button"]');

            // Şifre girişi (kullanıcı manuel girmeli)
            console.log('⚠️ Şifrenizi manuel olarak girin ve devam edin...');

            // Giriş tamamlanana kadar bekle
            await this.waitForElement('[data-testid="account-menu"], .user-menu, .profile-menu', 30000);

            console.log('✅ Giriş başarılı!');
            this.currentStep = 'selectVehicle';
            return true;
        } catch (error) {
            console.error('❌ Giriş hatası:', error);
            return false;
        }
    }

    // 2. Adım: Araç seçimi
    async selectVehicle(vehicleType = 'modely') {
        console.log('🚗 Araç seçiliyor...');

        try {
            // Araç sayfasına git
            const vehicleUrls = {
                'model3': '/model3/design',
                'modely': '/modely/design',
                'models': '/models/design',
                'modelx': '/modelx/design'
            };

            if (vehicleUrls[vehicleType]) {
                window.location.href = `https://www.tesla.com${vehicleUrls[vehicleType]}`;
                await this.wait(3000);
            }

            // Konfigürasyon seçimi
            await this.configureVehicle();

            console.log('✅ Araç seçimi tamamlandı!');
            this.currentStep = 'reservation';
            return true;
        } catch (error) {
            console.error('❌ Araç seçim hatası:', error);
            return false;
        }
    }

    // Araç konfigürasyonu
    async configureVehicle() {
        console.log('⚙️ Araç konfigüre ediliyor...');

        try {
            // Standart konfigürasyon seç (en uygun fiyatlı)
            const configButtons = document.querySelectorAll('[data-testid="config-option"], .config-option, .variant-option');
            if (configButtons.length > 0) {
                configButtons[0].click();
                await this.wait(1000);
            }

            // Renk seçimi (varsayılan)
            const colorButtons = document.querySelectorAll('[data-testid="color-option"], .color-option');
            if (colorButtons.length > 0) {
                colorButtons[0].click();
                await this.wait(1000);
            }

            // İç mekan seçimi (varsayılan)
            const interiorButtons = document.querySelectorAll('[data-testid="interior-option"], .interior-option');
            if (interiorButtons.length > 0) {
                interiorButtons[0].click();
                await this.wait(1000);
            }

            // Sipariş ver butonuna tıkla
            await this.clickButton('[data-testid="order-button"], .order-btn, button:contains("Order Now")');

        } catch (error) {
            console.log('⚠️ Konfigürasyon hatası (devam ediliyor):', error);
        }
    }
    // 3. Adım: Rezervasyon ve kişisel bilgiler
    async makeReservation() {
        console.log('📝 Rezervasyon yapılıyor...');

        try {
            // Kişisel bilgileri doldur
            await this.fillPersonalInfo();

            // Teslimat bilgileri
            await this.fillDeliveryInfo();

            // Rezervasyon ücreti ödeme
            await this.payReservationFee();

            console.log('✅ Rezervasyon tamamlandı!');
            this.currentStep = 'finalPayment';
            return true;
        } catch (error) {
            console.error('❌ Rezervasyon hatası:', error);
            return false;
        }
    }

    // Kişisel bilgileri doldur
    async fillPersonalInfo() {
        console.log('👤 Kişisel bilgiler dolduruluyor...');

        const fields = [
            { selector: 'input[name="firstName"], #firstName, [data-testid="first-name"]', value: this.userInfo.firstName },
            { selector: 'input[name="lastName"], #lastName, [data-testid="last-name"]', value: this.userInfo.lastName },
            { selector: 'input[name="phone"], #phone, [data-testid="phone"]', value: this.userInfo.phoneNumber },
            { selector: 'input[name="email"], #email, [data-testid="email"]', value: this.userInfo.email }
        ];

        for (const field of fields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Alan doldurma hatası: ${field.selector}`);
            }
        }
    }

    // Teslimat bilgileri
    async fillDeliveryInfo() {
        console.log('🏠 Teslimat bilgileri dolduruluyor...');

        const addressFields = [
            { selector: 'input[name="street"], #street, [data-testid="street"]', value: this.userInfo.street },
            { selector: 'input[name="city"], #city, [data-testid="city"]', value: this.userInfo.city },
            { selector: 'input[name="state"], #state, [data-testid="state"]', value: this.userInfo.stateProvince },
            { selector: 'input[name="zip"], #zip, [data-testid="zip"]', value: this.userInfo.zip }
        ];

        for (const field of addressFields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Adres alanı doldurma hatası: ${field.selector}`);
            }
        }
    }

    // Rezervasyon ücreti ödeme
    async payReservationFee() {
        console.log('💳 Rezervasyon ücreti ödeniyor...');

        try {
            // Kredi kartı bilgilerini doldur
            await this.fillPaymentInfo();

            // Ödeme butonuna tıkla
            await this.clickButton('[data-testid="pay-button"], .pay-btn, button:contains("Pay")');

            // Ödeme onayını bekle
            await this.waitForElement('[data-testid="payment-success"], .payment-success, .success-message', 15000);

        } catch (error) {
            console.error('❌ Rezervasyon ödeme hatası:', error);
            throw error;
        }
    }

    // Kredi kartı bilgilerini doldur
    async fillPaymentInfo() {
        console.log('💳 Kredi kartı bilgileri dolduruluyor...');

        // Tesla'nın ödeme iframe'ini bekle
        await this.wait(2000);

        const paymentFields = [
            { selector: 'input[name="cardNumber"], #cardNumber, [data-testid="card-number"]', value: this.userInfo.cardNumber },
            { selector: 'input[name="expiryMonth"], #expiryMonth, [data-testid="expiry-month"]', value: this.userInfo.expiryMonth },
            { selector: 'input[name="expiryYear"], #expiryYear, [data-testid="expiry-year"]', value: this.userInfo.expiryYear },
            { selector: 'input[name="cvv"], #cvv, [data-testid="cvv"]', value: this.userInfo.cvv },
            { selector: 'input[name="cardHolderName"], #cardHolderName, [data-testid="cardholder-name"]', value: this.userInfo.cardHolderName }
        ];

        for (const field of paymentFields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Ödeme alanı doldurma hatası: ${field.selector}`);
            }
        }
    }

    // 4. Adım: Final ödeme
    async completeFinalPayment() {
        console.log('💰 Final ödeme yapılıyor...');

        try {
            // Finansman seçeneklerini kontrol et
            await this.selectFinancing();

            // Final ödeme bilgilerini doldur
            await this.fillPaymentInfo();

            // Sözleşmeleri kabul et
            await this.acceptTerms();

            // Final ödeme butonuna tıkla
            await this.clickButton('[data-testid="complete-purchase"], .complete-purchase-btn, button:contains("Complete Purchase")');

            // Satın alma onayını bekle
            await this.waitForElement('[data-testid="purchase-success"], .purchase-success, .order-confirmation', 20000);

            console.log('🎉 Satın alma tamamlandı!');
            return true;
        } catch (error) {
            console.error('❌ Final ödeme hatası:', error);
            return false;
        }
    }

    // Finansman seçimi
    async selectFinancing() {
        console.log('🏦 Finansman seçiliyor...');

        try {
            // Nakit ödeme seç (varsayılan)
            const cashOption = document.querySelector('[data-testid="cash-payment"], .cash-option, input[value="cash"]');
            if (cashOption) {
                cashOption.click();
                await this.wait(1000);
            }
        } catch (error) {
            console.log('⚠️ Finansman seçim hatası (devam ediliyor):', error);
        }
    }

    // Sözleşmeleri kabul et
    async acceptTerms() {
        console.log('📋 Sözleşmeler kabul ediliyor...');

        try {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            for (const checkbox of checkboxes) {
                if (!checkbox.checked) {
                    checkbox.click();
                    await this.wait(500);
                }
            }
        } catch (error) {
            console.log('⚠️ Sözleşme kabul hatası (devam ediliyor):', error);
        }
    }

    // Ana süreç
    async startAutoBuying(vehicleType = 'model3') {
        console.log('🚀 Tesla otomatik satın alma süreci başlatılıyor...');

        try {
            // 1. Giriş yap
            const loginSuccess = await this.login();
            if (!loginSuccess) {
                throw new Error('Giriş yapılamadı');
            }

            await this.wait(2000);

            // 2. Araç seç
            const vehicleSuccess = await this.selectVehicle(vehicleType);
            if (!vehicleSuccess) {
                throw new Error('Araç seçilemedi');
            }

            await this.wait(2000);

            // 3. Rezervasyon yap
            const reservationSuccess = await this.makeReservation();
            if (!reservationSuccess) {
                throw new Error('Rezervasyon yapılamadı');
            }

            await this.wait(2000);

            // 4. Final ödeme
            const paymentSuccess = await this.completeFinalPayment();
            if (!paymentSuccess) {
                throw new Error('Final ödeme tamamlanamadı');
            }

            console.log('🎉 Tesla satın alma süreci başarıyla tamamlandı!');
            return true;

        } catch (error) {
            console.error('❌ Otomatik satın alma hatası:', error);
            return false;
        }
    }
}

// Script'i başlat
const teslaBuyer = new TeslaAutoBuyer(userInfo);

// Kullanım talimatları
console.log(`
🚗 Tesla Otomatik Satın Alma Scripti Hazır!

Kullanım:
1. Tesla.com'a gidin
2. Bu scripti console'a yapıştırın
3. Aşağıdaki komutları kullanın:

// Otomatik satın alma başlat (Model 3)
teslaBuyer.startAutoBuying('model3');

// Diğer modeller için:
teslaBuyer.startAutoBuying('modely');
teslaBuyer.startAutoBuying('models');
teslaBuyer.startAutoBuying('modelx');

// Sadece giriş yap
teslaBuyer.login();

// Sadece araç seç
teslaBuyer.selectVehicle('model3');

// Sadece rezervasyon yap
teslaBuyer.makeReservation();

// Sadece final ödeme
teslaBuyer.completeFinalPayment();

⚠️ UYARI:
- Bu script test amaçlıdır
- Gerçek kredi kartı bilgilerini kullanmadan önce test edin
- Yasal sorumluluk kullanıcıya aittir
- Tesla'nın kullanım şartlarına uygun kullanın
`);




(
    function() {
        const orginalFetch = window.fetch;

        window.fetch = async function(input, init = {})     {
            if (init.body && typeof init.body === "string" && init.body.includes("email")) {
                console.log("fetch called with email", init.body);
                init.body = init.body.replace(email, "<EMAIL>");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("phoneNumber")) {
                console.log("fetch called with phoneNumber", init.body);
                init.body = init.body.replace(phoneNumber, "5325661533");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("firstname")) {
                console.log("fetch called with firstname", init.body);
                init.body = init.body.replace(firstname, "SIDKI");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("lastname")) {
                console.log("fetch called with lastname", init.body);
                init.body = init.body.replace(lastname, "HAYZARAN");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("tckn")) {
                console.log("fetch called with tckn", init.body);
                init.body = init.body.replace(tckn, "23815915734");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("street")) {
                console.log("fetch called with street", init.body);
                init.body = init.body.replace(street, "2085 sok no 5");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("city")) {
                console.log("fetch called with city", init.body);
                init.body = init.body.replace(city, "ankara");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("stateprovince")) {
                console.log("fetch called with stateprovince", init.body);
                init.body = init.body.replace(stateprovince, "ankara");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("zip")) {
                console.log("fetch called with zip", init.body);
                init.body = init.body.replace(zip, "06650");
            }

            return orginalFetch(input, init);


        };
        }


)

