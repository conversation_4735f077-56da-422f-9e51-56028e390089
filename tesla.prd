// TESLA MODEL Y JUNIPER REZERVASYON SCRİPTİ
const userInfo = {
    email: "<EMAIL>",
    password: "?r8YN3t-7h^G$v/",
    phoneNumber: "5325661533",
    firstName: "SIDKI",
    lastName: "HAYZARAN",
    tckn: "23815915734",
    street: "2085 sok no 5",
    city: "ankara",
    stateProvince: "ankara",
    zip: "06650"
};

const modelYConfig = {
    model: "Model Y Arkadan Çekiş",
    exteriorColor: "Quicksilver",
    interiorColor: "All Black Premium İç Mekan",
    wheels: "19\" Crossflow Jantlar",
    expectedPrice: "1.913.987",
    country: "TR"
};

class TeslaModelYReservation {
    constructor(userInfo, modelYConfig) {
        this.userInfo = userInfo;
        this.modelYConfig = modelYConfig;
        this.reservationId = null;
        this.vinNumber = null;
        this.finalPrice = null;
        this.baseUrl = 'https://www.tesla.com/tr_tr';
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) return element;
            await this.wait(100);
        }
        throw new Error(`Element bulunamadı: ${selector}`);
    }

    async fillInput(selector, value) {
        const input = await this.waitForElement(selector);
        input.focus();
        input.value = '';
        input.value = value;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        await this.wait(500);
    }

    async clickButton(selector) {
        const button = await this.waitForElement(selector);
        button.scrollIntoView();
        await this.wait(500);
        button.click();
        await this.wait(1000);
    }

    async login() {
        console.log('🔐 Giriş yapılıyor...');

        try {
            if (!window.location.href.includes('tesla.com/tr_tr')) {
                window.location.href = this.baseUrl;
                await this.wait(3000);
            }

            await this.clickButton('a[href*="account"], .account-link, [data-testid="account-button"]');
            await this.fillInput('input[type="email"], input[name="email"], #email', this.userInfo.email);
            await this.clickButton('button[type="submit"], .btn-primary, [data-testid="continue-button"]');

            await this.wait(2000);
            await this.fillInput('input[type="password"], input[name="password"], #password', this.userInfo.password);
            await this.clickButton('button[type="submit"], .btn-primary, [data-testid="sign-in-button"]');

            await this.waitForElement('[data-testid="account-menu"], .user-menu, .profile-menu', 30000);
            console.log('✅ Giriş başarılı!');
            return true;
        } catch (error) {
            console.error('❌ Giriş hatası:', error);
            return false;
        }
    }

    async selectModelYJuniper() {
        console.log('🚗 Model Y Juniper seçiliyor...');

        try {
            window.location.href = `${this.baseUrl}/modely/design`;
            await this.wait(3000);

            await this.selectRearWheelDrive();
            await this.configureModelY();
            await this.checkPriceAndVIN();

            console.log('✅ Model Y seçimi tamamlandı!');
            console.log(`🏷️ VIN: ${this.vinNumber}`);
            console.log(`💰 Fiyat: ${this.finalPrice} TL`);

            return true;
        } catch (error) {
            console.error('❌ Model Y seçim hatası:', error);
            return false;
        }
    }

    async selectRearWheelDrive() {
        const rearWheelOptions = [
            '[data-testid="rwd-option"]',
            '.rwd-option',
            'button:contains("Arkadan Çekiş")',
            '[data-variant="rwd"]'
        ];

        for (const selector of rearWheelOptions) {
            try {
                await this.clickButton(selector);
                break;
            } catch (e) {
                continue;
            }
        }
        await this.wait(2000);
    }

    async configureModelY() {
        await this.selectExteriorColor();
        await this.selectInteriorColor();
        await this.selectWheels();
        await this.wait(2000);
    }

    async selectExteriorColor() {
        const colorSelectors = [
            '[data-testid="quicksilver-color"]',
            '.color-quicksilver',
            'button[aria-label*="Quicksilver"]',
            '.paint-option:contains("Quicksilver")'
        ];

        for (const selector of colorSelectors) {
            try {
                await this.clickButton(selector);
                console.log('✅ Quicksilver renk seçildi');
                break;
            } catch (e) {
                continue;
            }
        }
        await this.wait(1500);
    }

    async selectInteriorColor() {
        const interiorSelectors = [
            '[data-testid="all-black-premium"]',
            '.interior-all-black-premium',
            'button[aria-label*="All Black Premium"]',
            '.interior-option:contains("All Black Premium")'
        ];

        for (const selector of interiorSelectors) {
            try {
                await this.clickButton(selector);
                console.log('✅ All Black Premium iç mekan seçildi');
                break;
            } catch (e) {
                continue;
            }
        }
        await this.wait(1500);
    }

    async selectWheels() {
        const wheelSelectors = [
            '[data-testid="19-crossflow-wheels"]',
            '.wheels-19-crossflow',
            'button[aria-label*="19\\" Crossflow"]',
            '.wheel-option:contains("19\\" Crossflow")'
        ];

        for (const selector of wheelSelectors) {
            try {
                await this.clickButton(selector);
                console.log('✅ 19" Crossflow jantlar seçildi');
                break;
            } catch (e) {
                continue;
            }
        }
        await this.wait(1500);
    }

    async checkPriceAndVIN() {
        const priceSelectors = [
            '[data-testid="total-price"]',
            '.total-price',
            '.price-display',
            '.final-price'
        ];

        for (const selector of priceSelectors) {
            try {
                const priceElement = await this.waitForElement(selector, 3000);
                this.finalPrice = priceElement.textContent.trim();
                console.log(`💰 Fiyat: ${this.finalPrice}`);
                break;
            } catch (e) {
                continue;
            }
        }

        const vinSelectors = [
            '[data-testid="vin-number"]',
            '.vin-display',
            '.vehicle-vin',
            'span:contains("VIN")'
        ];

        for (const selector of vinSelectors) {
            try {
                const vinElement = await this.waitForElement(selector, 3000);
                this.vinNumber = vinElement.textContent.trim();
                console.log(`🏷️ VIN: ${this.vinNumber}`);
                break;
            } catch (e) {
                continue;
            }
        }
    }
    async makeReservation() {
        console.log('📝 Rezervasyon yapılıyor...');

        try {
            await this.fillPersonalInfo();
            await this.fillDeliveryInfo();
            await this.payReservationFee();

            console.log('✅ Rezervasyon tamamlandı!');
            return true;
        } catch (error) {
            console.error('❌ Rezervasyon hatası:', error);
            return false;
        }
    }

    async fillPersonalInfo() {
        const fields = [
            { selector: 'input[name="firstName"], #firstName, [data-testid="first-name"]', value: this.userInfo.firstName },
            { selector: 'input[name="lastName"], #lastName, [data-testid="last-name"]', value: this.userInfo.lastName },
            { selector: 'input[name="phone"], #phone, [data-testid="phone"]', value: this.userInfo.phoneNumber },
            { selector: 'input[name="email"], #email, [data-testid="email"]', value: this.userInfo.email }
        ];

        for (const field of fields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Alan doldurma hatası: ${field.selector}`);
            }
        }
    }

    async fillDeliveryInfo() {
        const addressFields = [
            { selector: 'input[name="street"], #street, [data-testid="street"]', value: this.userInfo.street },
            { selector: 'input[name="city"], #city, [data-testid="city"]', value: this.userInfo.city },
            { selector: 'input[name="state"], #state, [data-testid="state"]', value: this.userInfo.stateProvince },
            { selector: 'input[name="zip"], #zip, [data-testid="zip"]', value: this.userInfo.zip }
        ];

        for (const field of addressFields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Adres alanı doldurma hatası: ${field.selector}`);
            }
        }
    }

    async payReservationFee() {
        try {
            await this.clickButton('[data-testid="order-button"], .order-btn, button:contains("Sipariş Ver"), button:contains("Order Now")');
            await this.wait(3000);
            await this.displayReservationInfo();
            console.log('🔄 Tesla ödeme sayfasına yönlendiriliyor...');
            await this.waitForElement('[data-testid="payment-success"], .payment-success, .success-message, .reservation-confirmed', 15000);
        } catch (error) {
            console.error('❌ Rezervasyon ödeme hatası:', error);
            throw error;
        }
    }

    async displayReservationInfo() {
        const reservationInfo = {
            model: this.modelYConfig.model,
            exteriorColor: this.modelYConfig.exteriorColor,
            interiorColor: this.modelYConfig.interiorColor,
            wheels: this.modelYConfig.wheels,
            vin: this.vinNumber,
            price: this.finalPrice,
            customerName: `${this.userInfo.firstName} ${this.userInfo.lastName}`,
            email: this.userInfo.email
        };

        console.log('🚗 REZERVASYON BİLGİLERİ:');
        console.log('═══════════════════════════════');
        console.log(`📱 Model: ${reservationInfo.model}`);
        console.log(`🎨 Dış Renk: ${reservationInfo.exteriorColor}`);
        console.log(`🪑 İç Renk: ${reservationInfo.interiorColor}`);
        console.log(`🛞 Jantlar: ${reservationInfo.wheels}`);
        console.log(`🏷️ VIN: ${reservationInfo.vin || 'Henüz atanmadı'}`);
        console.log(`💰 Fiyat: ${reservationInfo.price || this.modelYConfig.expectedPrice + ' TL'}`);
        console.log(`👤 Müşteri: ${reservationInfo.customerName}`);
        console.log(`📧 Email: ${reservationInfo.email}`);
        console.log('═══════════════════════════════');

        try {
            const reservationElement = await this.waitForElement('[data-testid="reservation-id"], .reservation-number', 5000);
            this.reservationId = reservationElement.textContent.trim();
            console.log(`🎫 Rezervasyon ID: ${this.reservationId}`);
        } catch (e) {
            console.log('⚠️ Rezervasyon ID henüz oluşturulmadı');
        }
    }

    async startReservation() {
        console.log('� Tesla Model Y Juniper rezervasyon başlatılıyor...');

        try {
            const loginSuccess = await this.login();
            if (!loginSuccess) throw new Error('Giriş yapılamadı');

            await this.wait(2000);

            const vehicleSuccess = await this.selectModelYJuniper();
            if (!vehicleSuccess) throw new Error('Model Y seçilemedi');

            await this.wait(2000);

            const reservationSuccess = await this.makeReservation();
            if (!reservationSuccess) throw new Error('Rezervasyon yapılamadı');

            console.log('🎉 Model Y Juniper rezervasyon tamamlandı!');
            console.log('💳 Ödeme için ayrı scripti kullanın...');

            return true;

        } catch (error) {
            console.error('❌ Rezervasyon hatası:', error);
            return false;
        }
    }
}

const teslaReservation = new TeslaModelYReservation(userInfo, modelYConfig);

console.log(`
� TESLA MODEL Y JUNIPER REZERVASYON SCRİPTİ

YAPIŞTIRILACAK KOD:
teslaReservation.startReservation();

Bu script sadece rezervasyon yapar. Ödeme için ayrı script kullanın.
`);





(
    function() {
        const orginalFetch = window.fetch;

        window.fetch = async function(input, init = {})     {
            if (init.body && typeof init.body === "string" && init.body.includes("email")) {
                console.log("fetch called with email", init.body);
                init.body = init.body.replace(email, "<EMAIL>");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("phoneNumber")) {
                console.log("fetch called with phoneNumber", init.body);
                init.body = init.body.replace(phoneNumber, "5325661533");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("firstname")) {
                console.log("fetch called with firstname", init.body);
                init.body = init.body.replace(firstname, "SIDKI");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("lastname")) {
                console.log("fetch called with lastname", init.body);
                init.body = init.body.replace(lastname, "HAYZARAN");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("tckn")) {
                console.log("fetch called with tckn", init.body);
                init.body = init.body.replace(tckn, "23815915734");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("street")) {
                console.log("fetch called with street", init.body);
                init.body = init.body.replace(street, "2085 sok no 5");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("city")) {
                console.log("fetch called with city", init.body);
                init.body = init.body.replace(city, "ankara");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("stateprovince")) {
                console.log("fetch called with stateprovince", init.body);
                init.body = init.body.replace(stateprovince, "ankara");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("zip")) {
                console.log("fetch called with zip", init.body);
                init.body = init.body.replace(zip, "06650");
            }

            return orginalFetch(input, init);


        };
        }


)

