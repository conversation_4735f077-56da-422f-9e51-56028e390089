// Tesla Model Y Juniper Türkiye Otomatik Satış Scripti
const userInfo = {
    email: "<EMAIL>",
    phoneNumber: "5325661533",
    firstName: "SIDKI",
    lastName: "HAYZARAN",
    tckn: "23815915734",
    street: "2085 sok no 5",
    city: "ankara",
    stateProvince: "ankara",
    zip: "06650",
    // Kredi kartı bilgileri (güvenlik için şifrelenmiş olarak saklanmalı)
    cardNumber: "****************", // Test kartı
    expiryMonth: "12",
    expiryYear: "2025",
    cvv: "123",
    cardHolderName: "SIDKI HAYZARAN"
};

// Model Y Juniper Konfigürasyonu
const modelYConfig = {
    model: "Model Y Arkadan Çekiş",
    exteriorColor: "Quicksilver", // Dış Renk
    interiorColor: "All Black Premium İç Mekan", // <PERSON>ç Renk
    wheels: "19\" Crossflow Jantlar", // Jantlar
    expectedPrice: "1.913.987", // Beklenen fiyat (TL)
    country: "TR" // Türkiye
};

// Tesla Model Y Juniper Türkiye Otomatik Satış Süreci
class TeslaModelYBuyer {
    constructor(userInfo, modelYConfig) {
        this.userInfo = userInfo;
        this.modelYConfig = modelYConfig;
        this.currentStep = 'login';
        this.selectedVehicle = null;
        this.reservationId = null;
        this.vinNumber = null;
        this.finalPrice = null;
        this.baseUrl = 'https://www.tesla.com/tr_tr'; // Türkiye Tesla sitesi
        this.paymentUrl = 'https://static-assets-pay.tesla.com/v5/';
    }

    // Bekleme fonksiyonu
    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Element bulma ve bekleme
    async waitForElement(selector, timeout = 10000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) return element;
            await this.wait(100);
        }
        throw new Error(`Element bulunamadı: ${selector}`);
    }

    // Form doldurma
    async fillInput(selector, value) {
        const input = await this.waitForElement(selector);
        input.focus();
        input.value = '';
        input.value = value;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        await this.wait(500);
    }

    // Butona tıklama
    async clickButton(selector) {
        const button = await this.waitForElement(selector);
        button.scrollIntoView();
        await this.wait(500);
        button.click();
        await this.wait(1000);
    }

    // 1. Adım: Türkiye Tesla sitesinde giriş yapma
    async login() {
        console.log('🔐 Türkiye Tesla sitesinde giriş yapılıyor...');

        try {
            // Türkiye Tesla sitesine git
            if (!window.location.href.includes('tesla.com/tr_tr')) {
                window.location.href = this.baseUrl;
                await this.wait(3000);
            }

            // Giriş butonunu bul ve tıkla
            await this.clickButton('a[href*="account"], .account-link, [data-testid="account-button"]');

            // Email girişi
            await this.fillInput('input[type="email"], input[name="email"], #email', this.userInfo.email);

            // Devam butonu
            await this.clickButton('button[type="submit"], .btn-primary, [data-testid="continue-button"]');

            // Şifre girişi (kullanıcı manuel girmeli)
            console.log('⚠️ Şifrenizi manuel olarak girin ve devam edin...');

            // Giriş tamamlanana kadar bekle
            await this.waitForElement('[data-testid="account-menu"], .user-menu, .profile-menu', 30000);

            console.log('✅ Türkiye Tesla sitesinde giriş başarılı!');
            this.currentStep = 'selectModelY';
            return true;
        } catch (error) {
            console.error('❌ Giriş hatası:', error);
            return false;
        }
    }

    // 2. Adım: Model Y Juniper seçimi ve konfigürasyonu
    async selectModelYJuniper() {
        console.log('🚗 Model Y Juniper seçiliyor...');

        try {
            // Model Y sayfasına git
            window.location.href = `${this.baseUrl}/modely/design`;
            await this.wait(3000);

            // Model Y Arkadan Çekiş seçimi
            await this.selectRearWheelDrive();

            // Konfigürasyon seçimi
            await this.configureModelY();

            // Fiyat ve VIN kontrolü
            await this.checkPriceAndVIN();

            console.log('✅ Model Y Juniper seçimi tamamlandı!');
            console.log(`🏷️ VIN: ${this.vinNumber}`);
            console.log(`💰 Fiyat: ${this.finalPrice} TL`);

            this.currentStep = 'reservation';
            return true;
        } catch (error) {
            console.error('❌ Model Y seçim hatası:', error);
            return false;
        }
    }

    // Arkadan çekiş seçimi
    async selectRearWheelDrive() {
        console.log('⚙️ Arkadan çekiş seçiliyor...');

        try {
            // Arkadan çekiş butonunu bul ve tıkla
            const rearWheelOptions = [
                '[data-testid="rwd-option"]',
                '.rwd-option',
                'button:contains("Arkadan Çekiş")',
                '[data-variant="rwd"]'
            ];

            for (const selector of rearWheelOptions) {
                try {
                    await this.clickButton(selector);
                    break;
                } catch (e) {
                    continue;
                }
            }

            await this.wait(2000);
        } catch (error) {
            console.log('⚠️ Arkadan çekiş seçim hatası (devam ediliyor):', error);
        }
    }

    // Model Y Juniper konfigürasyonu
    async configureModelY() {
        console.log('⚙️ Model Y Juniper konfigüre ediliyor...');

        try {
            // Quicksilver renk seçimi
            await this.selectExteriorColor();

            // All Black Premium iç mekan seçimi
            await this.selectInteriorColor();

            // 19" Crossflow jantlar seçimi
            await this.selectWheels();

            // Ek özellikler (varsa)
            await this.selectAdditionalFeatures();

            await this.wait(2000);

        } catch (error) {
            console.log('⚠️ Model Y konfigürasyon hatası (devam ediliyor):', error);
        }
    }

    // Dış renk seçimi - Quicksilver
    async selectExteriorColor() {
        console.log('🎨 Quicksilver dış renk seçiliyor...');

        try {
            const colorSelectors = [
                '[data-testid="quicksilver-color"]',
                '.color-quicksilver',
                'button[aria-label*="Quicksilver"]',
                '.paint-option:contains("Quicksilver")'
            ];

            for (const selector of colorSelectors) {
                try {
                    await this.clickButton(selector);
                    console.log('✅ Quicksilver renk seçildi');
                    break;
                } catch (e) {
                    continue;
                }
            }

            await this.wait(1500);
        } catch (error) {
            console.log('⚠️ Quicksilver renk seçim hatası:', error);
        }
    }

    // İç renk seçimi - All Black Premium
    async selectInteriorColor() {
        console.log('🪑 All Black Premium iç mekan seçiliyor...');

        try {
            const interiorSelectors = [
                '[data-testid="all-black-premium"]',
                '.interior-all-black-premium',
                'button[aria-label*="All Black Premium"]',
                '.interior-option:contains("All Black Premium")'
            ];

            for (const selector of interiorSelectors) {
                try {
                    await this.clickButton(selector);
                    console.log('✅ All Black Premium iç mekan seçildi');
                    break;
                } catch (e) {
                    continue;
                }
            }

            await this.wait(1500);
        } catch (error) {
            console.log('⚠️ All Black Premium iç mekan seçim hatası:', error);
        }
    }

    // Jant seçimi - 19" Crossflow
    async selectWheels() {
        console.log('🛞 19" Crossflow jantlar seçiliyor...');

        try {
            const wheelSelectors = [
                '[data-testid="19-crossflow-wheels"]',
                '.wheels-19-crossflow',
                'button[aria-label*="19\\" Crossflow"]',
                '.wheel-option:contains("19\\" Crossflow")'
            ];

            for (const selector of wheelSelectors) {
                try {
                    await this.clickButton(selector);
                    console.log('✅ 19" Crossflow jantlar seçildi');
                    break;
                } catch (e) {
                    continue;
                }
            }

            await this.wait(1500);
        } catch (error) {
            console.log('⚠️ 19" Crossflow jant seçim hatası:', error);
        }
    }

    // Ek özellikler
    async selectAdditionalFeatures() {
        console.log('⭐ Ek özellikler kontrol ediliyor...');

        try {
            // Autopilot veya FSD seçenekleri varsa
            const autopilotOptions = document.querySelectorAll('[data-testid*="autopilot"], .autopilot-option');
            if (autopilotOptions.length > 0) {
                console.log('🤖 Autopilot seçenekleri bulundu');
            }

            await this.wait(1000);
        } catch (error) {
            console.log('⚠️ Ek özellik seçim hatası:', error);
        }
    }

    // Fiyat ve VIN kontrolü
    async checkPriceAndVIN() {
        console.log('💰 Fiyat ve VIN kontrol ediliyor...');

        try {
            // Fiyat bilgisini al
            const priceSelectors = [
                '[data-testid="total-price"]',
                '.total-price',
                '.price-display',
                '.final-price'
            ];

            for (const selector of priceSelectors) {
                try {
                    const priceElement = await this.waitForElement(selector, 3000);
                    this.finalPrice = priceElement.textContent.trim();
                    console.log(`💰 Tespit edilen fiyat: ${this.finalPrice}`);
                    break;
                } catch (e) {
                    continue;
                }
            }

            // VIN bilgisini al
            const vinSelectors = [
                '[data-testid="vin-number"]',
                '.vin-display',
                '.vehicle-vin',
                'span:contains("VIN")'
            ];

            for (const selector of vinSelectors) {
                try {
                    const vinElement = await this.waitForElement(selector, 3000);
                    this.vinNumber = vinElement.textContent.trim();
                    console.log(`🏷️ Tespit edilen VIN: ${this.vinNumber}`);
                    break;
                } catch (e) {
                    continue;
                }
            }

            // Beklenen fiyatla karşılaştır
            if (this.finalPrice && this.finalPrice.includes(this.modelYConfig.expectedPrice)) {
                console.log('✅ Fiyat beklenen değerle eşleşiyor');
            } else {
                console.log('⚠️ Fiyat farklı olabilir, kontrol edin');
            }

        } catch (error) {
            console.log('⚠️ Fiyat/VIN kontrol hatası:', error);
        }
    }
    // 3. Adım: Rezervasyon ve kişisel bilgiler
    async makeReservation() {
        console.log('📝 Rezervasyon yapılıyor...');

        try {
            // Kişisel bilgileri doldur
            await this.fillPersonalInfo();

            // Teslimat bilgileri
            await this.fillDeliveryInfo();

            // Rezervasyon ücreti ödeme
            await this.payReservationFee();

            console.log('✅ Rezervasyon tamamlandı!');
            this.currentStep = 'finalPayment';
            return true;
        } catch (error) {
            console.error('❌ Rezervasyon hatası:', error);
            return false;
        }
    }

    // Kişisel bilgileri doldur
    async fillPersonalInfo() {
        console.log('👤 Kişisel bilgiler dolduruluyor...');

        const fields = [
            { selector: 'input[name="firstName"], #firstName, [data-testid="first-name"]', value: this.userInfo.firstName },
            { selector: 'input[name="lastName"], #lastName, [data-testid="last-name"]', value: this.userInfo.lastName },
            { selector: 'input[name="phone"], #phone, [data-testid="phone"]', value: this.userInfo.phoneNumber },
            { selector: 'input[name="email"], #email, [data-testid="email"]', value: this.userInfo.email }
        ];

        for (const field of fields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Alan doldurma hatası: ${field.selector}`);
            }
        }
    }

    // Teslimat bilgileri
    async fillDeliveryInfo() {
        console.log('🏠 Teslimat bilgileri dolduruluyor...');

        const addressFields = [
            { selector: 'input[name="street"], #street, [data-testid="street"]', value: this.userInfo.street },
            { selector: 'input[name="city"], #city, [data-testid="city"]', value: this.userInfo.city },
            { selector: 'input[name="state"], #state, [data-testid="state"]', value: this.userInfo.stateProvince },
            { selector: 'input[name="zip"], #zip, [data-testid="zip"]', value: this.userInfo.zip }
        ];

        for (const field of addressFields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Adres alanı doldurma hatası: ${field.selector}`);
            }
        }
    }

    // Rezervasyon ücreti ödeme
    async payReservationFee() {
        console.log('💳 Rezervasyon ücreti ödeniyor...');

        try {
            // Sipariş ver butonuna tıkla
            await this.clickButton('[data-testid="order-button"], .order-btn, button:contains("Sipariş Ver"), button:contains("Order Now")');

            // Rezervasyon sayfasına yönlendirilmeyi bekle
            await this.wait(3000);

            // Rezervasyon bilgilerini göster
            await this.displayReservationInfo();

            // Tesla ödeme sayfasına yönlendir
            console.log('🔄 Tesla ödeme sayfasına yönlendiriliyor...');

            // Ödeme onayını bekle
            await this.waitForElement('[data-testid="payment-success"], .payment-success, .success-message, .reservation-confirmed', 15000);

        } catch (error) {
            console.error('❌ Rezervasyon ödeme hatası:', error);
            throw error;
        }
    }

    // Rezervasyon bilgilerini göster
    async displayReservationInfo() {
        console.log('📋 Rezervasyon bilgileri gösteriliyor...');

        try {
            const reservationInfo = {
                model: this.modelYConfig.model,
                exteriorColor: this.modelYConfig.exteriorColor,
                interiorColor: this.modelYConfig.interiorColor,
                wheels: this.modelYConfig.wheels,
                vin: this.vinNumber,
                price: this.finalPrice,
                customerName: `${this.userInfo.firstName} ${this.userInfo.lastName}`,
                email: this.userInfo.email
            };

            console.log('🚗 REZERVASYON BİLGİLERİ:');
            console.log('═══════════════════════════════');
            console.log(`📱 Model: ${reservationInfo.model}`);
            console.log(`🎨 Dış Renk: ${reservationInfo.exteriorColor}`);
            console.log(`🪑 İç Renk: ${reservationInfo.interiorColor}`);
            console.log(`🛞 Jantlar: ${reservationInfo.wheels}`);
            console.log(`🏷️ VIN: ${reservationInfo.vin || 'Henüz atanmadı'}`);
            console.log(`💰 Fiyat: ${reservationInfo.price || this.modelYConfig.expectedPrice + ' TL'}`);
            console.log(`👤 Müşteri: ${reservationInfo.customerName}`);
            console.log(`📧 Email: ${reservationInfo.email}`);
            console.log('═══════════════════════════════');

            // Rezervasyon ID'sini al (varsa)
            try {
                const reservationElement = await this.waitForElement('[data-testid="reservation-id"], .reservation-number', 5000);
                this.reservationId = reservationElement.textContent.trim();
                console.log(`🎫 Rezervasyon ID: ${this.reservationId}`);
            } catch (e) {
                console.log('⚠️ Rezervasyon ID henüz oluşturulmadı');
            }

        } catch (error) {
            console.log('⚠️ Rezervasyon bilgi gösterme hatası:', error);
        }
    }

    // Tesla ödeme sayfası için kredi kartı scripti
    async fillPaymentInfoOnTeslaPaymentSite() {
        console.log('💳 Tesla ödeme sayfasında kredi kartı bilgileri dolduruluyor...');

        try {
            // Tesla ödeme sayfasının yüklenmesini bekle
            if (!window.location.href.includes('static-assets-pay.tesla.com')) {
                console.log('⚠️ Tesla ödeme sayfasına yönlendirilmeyi bekleyin...');
                return false;
            }

            // Ödeme iframe'ini bekle
            await this.wait(3000);

            const paymentFields = [
                { selector: 'input[name="cardNumber"], #cardNumber, [data-testid="card-number"], .card-number-input', value: this.userInfo.cardNumber },
                { selector: 'input[name="expiryMonth"], #expiryMonth, [data-testid="expiry-month"], .expiry-month-input', value: this.userInfo.expiryMonth },
                { selector: 'input[name="expiryYear"], #expiryYear, [data-testid="expiry-year"], .expiry-year-input', value: this.userInfo.expiryYear },
                { selector: 'input[name="cvv"], #cvv, [data-testid="cvv"], .cvv-input', value: this.userInfo.cvv },
                { selector: 'input[name="cardHolderName"], #cardHolderName, [data-testid="cardholder-name"], .cardholder-name-input', value: this.userInfo.cardHolderName }
            ];

            for (const field of paymentFields) {
                try {
                    await this.fillInput(field.selector, field.value);
                    console.log(`✅ ${field.selector.split(',')[0]} alanı dolduruldu`);
                } catch (error) {
                    console.log(`⚠️ Ödeme alanı doldurma hatası: ${field.selector.split(',')[0]}`);
                }
            }

            // Fatura adresi bilgilerini doldur
            await this.fillBillingAddress();

            return true;

        } catch (error) {
            console.error('❌ Tesla ödeme sayfası doldurma hatası:', error);
            return false;
        }
    }

    // Fatura adresi bilgilerini doldur
    async fillBillingAddress() {
        console.log('🏠 Fatura adresi bilgileri dolduruluyor...');

        const billingFields = [
            { selector: 'input[name="firstName"], #firstName, [data-testid="billing-first-name"]', value: this.userInfo.firstName },
            { selector: 'input[name="lastName"], #lastName, [data-testid="billing-last-name"]', value: this.userInfo.lastName },
            { selector: 'input[name="address"], #address, [data-testid="billing-address"]', value: this.userInfo.street },
            { selector: 'input[name="city"], #city, [data-testid="billing-city"]', value: this.userInfo.city },
            { selector: 'input[name="postalCode"], #postalCode, [data-testid="billing-postal-code"]', value: this.userInfo.zip },
            { selector: 'input[name="phone"], #phone, [data-testid="billing-phone"]', value: this.userInfo.phoneNumber }
        ];

        for (const field of billingFields) {
            try {
                await this.fillInput(field.selector, field.value);
            } catch (error) {
                console.log(`⚠️ Fatura adresi alanı doldurma hatası: ${field.selector.split(',')[0]}`);
            }
        }
    }

    // Tesla ödeme sayfasında ödemeyi tamamla
    async completeTeslaPayment() {
        console.log('💰 Tesla ödeme sayfasında ödeme tamamlanıyor...');

        try {
            // Kredi kartı bilgilerini doldur
            const paymentFilled = await this.fillPaymentInfoOnTeslaPaymentSite();
            if (!paymentFilled) {
                throw new Error('Ödeme bilgileri doldurulamadı');
            }

            // Sözleşmeleri kabul et
            await this.acceptTermsOnPaymentSite();

            // Ödeme butonuna tıkla
            const paymentButtons = [
                '[data-testid="complete-payment"]',
                '.complete-payment-btn',
                'button:contains("Ödemeyi Tamamla")',
                'button:contains("Complete Payment")',
                '.pay-now-btn'
            ];

            for (const selector of paymentButtons) {
                try {
                    await this.clickButton(selector);
                    console.log('✅ Ödeme butonu tıklandı');
                    break;
                } catch (e) {
                    continue;
                }
            }

            // Ödeme onayını bekle
            await this.waitForElement('[data-testid="payment-success"], .payment-success, .order-confirmation, .success-message', 30000);

            console.log('🎉 Tesla ödeme sayfasında ödeme başarıyla tamamlandı!');
            return true;

        } catch (error) {
            console.error('❌ Tesla ödeme sayfası ödeme hatası:', error);
            return false;
        }
    }

    // Ödeme sayfasında sözleşmeleri kabul et
    async acceptTermsOnPaymentSite() {
        console.log('📋 Ödeme sayfasında sözleşmeler kabul ediliyor...');

        try {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            for (const checkbox of checkboxes) {
                if (!checkbox.checked) {
                    checkbox.click();
                    await this.wait(500);
                    console.log('✅ Sözleşme kabul edildi');
                }
            }
        } catch (error) {
            console.log('⚠️ Sözleşme kabul hatası (devam ediliyor):', error);
        }
    }

    // 4. Adım: Final ödeme
    async completeFinalPayment() {
        console.log('💰 Final ödeme yapılıyor...');

        try {
            // Finansman seçeneklerini kontrol et
            await this.selectFinancing();

            // Final ödeme bilgilerini doldur
            await this.fillPaymentInfo();

            // Sözleşmeleri kabul et
            await this.acceptTerms();

            // Final ödeme butonuna tıkla
            await this.clickButton('[data-testid="complete-purchase"], .complete-purchase-btn, button:contains("Complete Purchase")');

            // Satın alma onayını bekle
            await this.waitForElement('[data-testid="purchase-success"], .purchase-success, .order-confirmation', 20000);

            console.log('🎉 Satın alma tamamlandı!');
            return true;
        } catch (error) {
            console.error('❌ Final ödeme hatası:', error);
            return false;
        }
    }

    // Finansman seçimi
    async selectFinancing() {
        console.log('🏦 Finansman seçiliyor...');

        try {
            // Nakit ödeme seç (varsayılan)
            const cashOption = document.querySelector('[data-testid="cash-payment"], .cash-option, input[value="cash"]');
            if (cashOption) {
                cashOption.click();
                await this.wait(1000);
            }
        } catch (error) {
            console.log('⚠️ Finansman seçim hatası (devam ediliyor):', error);
        }
    }

    // Sözleşmeleri kabul et
    async acceptTerms() {
        console.log('📋 Sözleşmeler kabul ediliyor...');

        try {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            for (const checkbox of checkboxes) {
                if (!checkbox.checked) {
                    checkbox.click();
                    await this.wait(500);
                }
            }
        } catch (error) {
            console.log('⚠️ Sözleşme kabul hatası (devam ediliyor):', error);
        }
    }

    // Ana süreç - Model Y Juniper Türkiye
    async startModelYJuniperBuying() {
        console.log('🚀 Tesla Model Y Juniper Türkiye otomatik satın alma süreci başlatılıyor...');

        try {
            // 1. Türkiye Tesla sitesinde giriş yap
            const loginSuccess = await this.login();
            if (!loginSuccess) {
                throw new Error('Giriş yapılamadı');
            }

            await this.wait(2000);

            // 2. Model Y Juniper seç ve konfigüre et
            const vehicleSuccess = await this.selectModelYJuniper();
            if (!vehicleSuccess) {
                throw new Error('Model Y seçilemedi');
            }

            await this.wait(2000);

            // 3. Rezervasyon yap
            const reservationSuccess = await this.makeReservation();
            if (!reservationSuccess) {
                throw new Error('Rezervasyon yapılamadı');
            }

            await this.wait(2000);

            // 4. Tesla ödeme sayfasında final ödeme
            console.log('🔄 Tesla ödeme sayfasına geçiliyor...');
            console.log('⚠️ Eğer Tesla ödeme sayfasına yönlendirilmediyseniz, manuel olarak gidin ve aşağıdaki komutu çalıştırın:');
            console.log('teslaBuyer.completeTeslaPayment()');

            console.log('🎉 Model Y Juniper rezervasyon süreci tamamlandı!');
            console.log('💳 Ödeme için Tesla ödeme sayfasını bekleyin...');

            return true;

        } catch (error) {
            console.error('❌ Model Y Juniper satın alma hatası:', error);
            return false;
        }
    }

    // Tesla ödeme sayfası için ayrı fonksiyon
    async completeTeslaPayment() {
        console.log('💳 Tesla ödeme sayfasında ödeme işlemi başlatılıyor...');

        try {
            // Tesla ödeme sayfasında mı kontrol et
            if (!window.location.href.includes('static-assets-pay.tesla.com')) {
                console.log('❌ Bu fonksiyon sadece Tesla ödeme sayfasında çalışır');
                console.log('🔗 Tesla ödeme sayfası: https://static-assets-pay.tesla.com/v5/');
                return false;
            }

            // Ödemeyi tamamla
            const paymentSuccess = await this.completeTeslaPayment();
            if (!paymentSuccess) {
                throw new Error('Tesla ödeme sayfasında ödeme tamamlanamadı');
            }

            console.log('🎉 Tesla Model Y Juniper satın alma süreci başarıyla tamamlandı!');
            return true;

        } catch (error) {
            console.error('❌ Tesla ödeme sayfası hatası:', error);
            return false;
        }
    }
}

// Script'i başlat
const teslaBuyer = new TeslaModelYBuyer(userInfo, modelYConfig);

// Kullanım talimatları
console.log(`
🚗 Tesla Model Y Juniper Türkiye Otomatik Satın Alma Scripti Hazır!

📋 HEDEF KONFİGÜRASYON:
═══════════════════════════════════════════════════════════
🚙 Model: ${modelYConfig.model}
🎨 Dış Renk: ${modelYConfig.exteriorColor}
🪑 İç Renk: ${modelYConfig.interiorColor}
🛞 Jantlar: ${modelYConfig.wheels}
💰 Beklenen Fiyat: ${modelYConfig.expectedPrice} TL
🌍 Ülke: Türkiye
═══════════════════════════════════════════════════════════

🚀 KULLANIM TALİMATLARI:

1️⃣ ADIM 1: Tesla Türkiye sitesine gidin
   👉 https://www.tesla.com/tr_tr

2️⃣ ADIM 2: Bu scripti console'a yapıştırın (F12 > Console)

3️⃣ ADIM 3: Otomatik satın alma başlatın:
   👉 teslaBuyer.startModelYJuniperBuying();

4️⃣ ADIM 4: Tesla ödeme sayfasında (https://static-assets-pay.tesla.com/v5/):
   👉 teslaBuyer.completeTeslaPayment();

🔧 MANUEL KONTROL KOMUTLARİ:
═══════════════════════════════════════════════════════════
// Sadece giriş yap
teslaBuyer.login();

// Sadece Model Y seç ve konfigüre et
teslaBuyer.selectModelYJuniper();

// Sadece rezervasyon yap
teslaBuyer.makeReservation();

// Rezervasyon bilgilerini göster
teslaBuyer.displayReservationInfo();

// Tesla ödeme sayfasında ödeme tamamla
teslaBuyer.completeTeslaPayment();

📊 REZERVASYON TAKİBİ:
═══════════════════════════════════════════════════════════
Script çalıştırıldığında şu bilgiler gösterilecek:
- VIN Numarası (XP7Y264_c28b766506d1d26d4cf6338eae2f284a formatında)
- Final Fiyat (1.913.987 TL formatında)
- Rezervasyon ID
- Müşteri bilgileri

⚠️ ÖNEMLİ UYARILAR:
═══════════════════════════════════════════════════════════
🔒 Bu script test amaçlıdır
💳 Gerçek kredi kartı bilgilerini kullanmadan önce test kartı ile deneyin
⚖️ Yasal sorumluluk kullanıcıya aittir
📜 Tesla'nın kullanım şartlarına uygun kullanın
🇹🇷 Sadece Türkiye Tesla sitesi için optimize edilmiştir
💰 Fiyat değişiklikleri olabilir, kontrol edin

🎯 BAŞLATMAK İÇİN:
teslaBuyer.startModelYJuniperBuying();
`);




(
    function() {
        const orginalFetch = window.fetch;

        window.fetch = async function(input, init = {})     {
            if (init.body && typeof init.body === "string" && init.body.includes("email")) {
                console.log("fetch called with email", init.body);
                init.body = init.body.replace(email, "<EMAIL>");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("phoneNumber")) {
                console.log("fetch called with phoneNumber", init.body);
                init.body = init.body.replace(phoneNumber, "5325661533");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("firstname")) {
                console.log("fetch called with firstname", init.body);
                init.body = init.body.replace(firstname, "SIDKI");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("lastname")) {
                console.log("fetch called with lastname", init.body);
                init.body = init.body.replace(lastname, "HAYZARAN");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("tckn")) {
                console.log("fetch called with tckn", init.body);
                init.body = init.body.replace(tckn, "23815915734");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("street")) {
                console.log("fetch called with street", init.body);
                init.body = init.body.replace(street, "2085 sok no 5");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("city")) {
                console.log("fetch called with city", init.body);
                init.body = init.body.replace(city, "ankara");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("stateprovince")) {
                console.log("fetch called with stateprovince", init.body);
                init.body = init.body.replace(stateprovince, "ankara");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("zip")) {
                console.log("fetch called with zip", init.body);
                init.body = init.body.replace(zip, "06650");
            }

            return orginalFetch(input, init);


        };
        }


)

