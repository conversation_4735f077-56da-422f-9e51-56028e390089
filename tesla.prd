const email=   "<EMAIL>";
const phoneNumber= "5325661533";
const firstname= "SIDKI";
const lastname= "HAYZARAN";
const tckn ="23815915734"   
const street ="2085 sok no 5";
const city="ankara";
cont stateprovince="ankara";
const zip="06650";




(
    function() {
        const orginalFetch = window.fetch;

        window.fetch = async function(input, init = {})     {
            if (init.body && typeof init.body === "string" && init.body.includes("email")) {
                console.log("fetch called with email", init.body);
                init.body = init.body.replace(email, "<EMAIL>");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("phoneNumber")) {
                console.log("fetch called with phoneNumber", init.body);
                init.body = init.body.replace(phoneNumber, "5325661533");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("firstname")) {
                console.log("fetch called with firstname", init.body);
                init.body = init.body.replace(firstname, "SIDKI");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("lastname")) {
                console.log("fetch called with lastname", init.body);
                init.body = init.body.replace(lastname, "HAYZARAN");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("tckn")) {
                console.log("fetch called with tckn", init.body);
                init.body = init.body.replace(tckn, "23815915734");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("street")) {
                console.log("fetch called with street", init.body);
                init.body = init.body.replace(street, "2085 sok no 5");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("city")) {
                console.log("fetch called with city", init.body);
                init.body = init.body.replace(city, "ankara");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("stateprovince")) {
                console.log("fetch called with stateprovince", init.body);
                init.body = init.body.replace(stateprovince, "ankara");
            }
            if (init.body && typeof init.body === "string" && init.body.includes("zip")) {  
                console.log("fetch called with zip", init.body);
                init.body = init.body.replace(zip, "06650");    
            }

            return orginalFetch(input, init);


        };
        }


)

